import { serve } from 'https://deno.land/std@0.190.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.39.7';
import nodemailer from 'npm:nodemailer';
serve(async (req)=>{
  // 1. Setup Supabase client
  const supabase = createClient('http://127.0.0.1:54321', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU');
  // 2. Fetch notification settings
  const { data: settings, error: settingsError } = await supabase.from('notification_settings').select('*').single();
  if (settingsError || !settings?.email_enabled) {
    return new Response(JSON.stringify({
      error: 'Email notifications disabled or settings error'
    }), {
      status: 400
    });
  }
  // 3. Calculate target date
  const daysBefore = settings.days_before_maturity_email || 3;
  const targetDate = new Date();
  targetDate.setDate(targetDate.getDate() + daysBefore);
  const targetDateStr = targetDate.toISOString().split('T')[0];
  // 4. Query investments maturing in X days
  const { data: investments, error: invError } = await supabase.from('investments').select(`
      id,
      maturity_date,
      scheme_name,
      primary_applicant_cif_id,
      secondary_applicant_cif_id
    `).eq('maturity_date', targetDateStr).eq('status', 'active');
  if (invError) {
    return new Response(JSON.stringify({
      error: 'Failed to fetch investments'
    }), {
      status: 500
    });
  }
  // 5. Collect all unique CIF IDs for primary and secondary applicants
  const cifIds = new Set();
  for (const inv of investments || []){
    if (inv.primary_applicant_cif_id) cifIds.add(inv.primary_applicant_cif_id);
    if (inv.secondary_applicant_cif_id) cifIds.add(inv.secondary_applicant_cif_id);
  }
  // 6. Fetch client emails for all relevant CIF IDs
  const { data: clients, error: clientsError } = await supabase.from('clients').select('cif_id, email, first_name, last_name').in('cif_id', Array.from(cifIds));
  if (clientsError) {
    return new Response(JSON.stringify({
      error: 'Failed to fetch client emails'
    }), {
      status: 500
    });
  }
  // 7. Map CIF ID to client info
  const clientMap = new Map();
  for (const c of clients || []){
    if (c.email) {
      clientMap.set(c.cif_id, {
        email: c.email,
        name: `${c.first_name || ''} ${c.last_name || ''}`.trim()
      });
    }
  }
  // 8. Setup nodemailer transporter
  const transporter = nodemailer.createTransport({
    host: 'smtp.gmail.com',
    port: 587,
    secure: false,
    auth: {
      user: '<EMAIL>',
      pass: 'pkhjogzbldwssfvf'
    }
  });
  // 9. Send emails
  let sent = 0;
  let errors = [];
  for (const inv of investments || []){
    const recipients = [];
    if (inv.primary_applicant_cif_id && clientMap.has(inv.primary_applicant_cif_id)) {
      recipients.push(clientMap.get(inv.primary_applicant_cif_id));
    }
    if (inv.secondary_applicant_cif_id && clientMap.has(inv.secondary_applicant_cif_id) && inv.secondary_applicant_cif_id !== inv.primary_applicant_cif_id) {
      recipients.push(clientMap.get(inv.secondary_applicant_cif_id));
    }
    for (const recipient of recipients){
      try {
        await transporter.sendMail({
          from: settings.smtp_username,
          to: recipient.email,
          subject: `Your investment in ${inv.scheme_name} is maturing soon`,
          html: `
  <div style="font-family: Arial, sans-serif; color: #333; max-width: 600px; margin: auto; border: 1px solid #e0e0e0; border-radius: 8px; padding: 20px; background-color: #fafafa;">
    <div style="text-align: center; margin-bottom: 20px;">
      <h2 style="color: #2c3e50;">📢 Investment Maturity Alert</h2>
    </div>
    <p>Dear <strong>${recipient.name || 'Investor'}</strong>,</p>
    <p>We would like to inform you that your investment is nearing its maturity date. Please find the details below:</p>
    <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
      <tr style="background-color: #f0f0f0;">
        <td style="padding: 10px; font-weight: bold;">Scheme Name:</td>
        <td style="padding: 10px;">${inv.scheme_name}</td>
      </tr>
      <tr>
        <td style="padding: 10px; font-weight: bold;">Maturity Date:</td>
        <td style="padding: 10px;">${inv.maturity_date}</td>
      </tr>
    </table>
    <p>👉 Please ensure any required actions are taken before the maturity date to avoid disruptions or lapses.</p>
    <p>If you have any questions or require assistance, feel free to contact our support team.</p>
    <div style="margin-top: 30px; text-align: center;">
      <p style="font-size: 14px; color: #888;">Thank you for investing with us.</p>
      <p style="font-size: 14px; color: #888;">– Desai Investments</p>
    </div>
  </div>
`
        });
        sent++;
      } catch (err) {
        errors.push({
          email: recipient.email,
          error: err.message
        });
      }
    }
  }
  return new Response(JSON.stringify({
    success: true,
    sent,
    errors
  }), {
    status: 200,
    headers: {
      'Content-Type': 'application/json'
    }
  });
});
