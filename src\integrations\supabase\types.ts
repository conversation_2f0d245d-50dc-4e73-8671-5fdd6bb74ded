export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instanciate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "12.2.3 (519615d)"
  }
  public: {
    Tables: {
      alerts: {
        Row: {
          alert_date: string
          alert_type: string
          channel: string
          client_id: string | null
          created_at: string | null
          id: string
          investment_id: string | null
          message: string
          status: string | null
          updated_at: string | null
        }
        Insert: {
          alert_date: string
          alert_type: string
          channel: string
          client_id?: string | null
          created_at?: string | null
          id?: string
          investment_id?: string | null
          message: string
          status?: string | null
          updated_at?: string | null
        }
        Update: {
          alert_date?: string
          alert_type?: string
          channel?: string
          client_id?: string | null
          created_at?: string | null
          id?: string
          investment_id?: string | null
          message?: string
          status?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "alerts_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "alerts_investment_id_fkey"
            columns: ["investment_id"]
            isOneToOne: false
            referencedRelation: "investments"
            referencedColumns: ["id"]
          },
        ]
      }
      client_sb_accounts: {
        Row: {
          added_at: string
          client_id: string
          id: string
          role: string
          sb_account_id: string
        }
        Insert: {
          added_at?: string
          client_id: string
          id?: string
          role?: string
          sb_account_id: string
        }
        Update: {
          added_at?: string
          client_id?: string
          id?: string
          role?: string
          sb_account_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "client_sb_accounts_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "client_sb_accounts_sb_account_id_fkey"
            columns: ["sb_account_id"]
            isOneToOne: false
            referencedRelation: "sb_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      clients: {
        Row: {
          aadhar_number: string | null
          aadhar_photo_url: string | null
          address: string | null
          care_of: string | null
          cif_id: string | null
          city: string | null
          client_photo_url: string | null
          contact_person2: string | null
          country: string | null
          country_code: string | null
          created_at: string | null
          email: string | null
          first_name: string
          id: string
          is_deleted: boolean | null
          last_name: string | null
          mobile_number: string
          pan_card_number: string | null
          pan_photo_url: string | null
          pincode: string | null
          state: string | null
          updated_at: string | null
          village: string | null
        }
        Insert: {
          aadhar_number?: string | null
          aadhar_photo_url?: string | null
          address?: string | null
          care_of?: string | null
          cif_id?: string | null
          city?: string | null
          client_photo_url?: string | null
          contact_person2?: string | null
          country?: string | null
          country_code?: string | null
          created_at?: string | null
          email?: string | null
          first_name: string
          id?: string
          is_deleted?: boolean | null
          last_name?: string | null
          mobile_number: string
          pan_card_number?: string | null
          pan_photo_url?: string | null
          pincode?: string | null
          state?: string | null
          updated_at?: string | null
          village?: string | null
        }
        Update: {
          aadhar_number?: string | null
          aadhar_photo_url?: string | null
          address?: string | null
          care_of?: string | null
          cif_id?: string | null
          city?: string | null
          client_photo_url?: string | null
          contact_person2?: string | null
          country?: string | null
          country_code?: string | null
          created_at?: string | null
          email?: string | null
          first_name?: string
          id?: string
          is_deleted?: boolean | null
          last_name?: string | null
          mobile_number?: string
          pan_card_number?: string | null
          pan_photo_url?: string | null
          pincode?: string | null
          state?: string | null
          updated_at?: string | null
          village?: string | null
        }
        Relationships: []
      }
      investments: {
        Row: {
          actual_profit: number
          amount: number
          client_id: string
          commission_percentage: number | null
          compounding_frequency: string | null
          created_at: string | null
          id: string
          interest_rate: number
          interest_type: string
          investment_date: string
          investment_type: string | null
          is_active: boolean | null
          lock_in_period_months: number
          maturity_amount: number
          maturity_date: string
          max_amount: number | null
          min_amount: number | null
          nominee_id: string | null
          payout_status: string | null
          primary_applicant_cif_id: string | null
          reinvestment_source_id: string | null
          remark: string | null
          scheme_code: string
          scheme_id: string
          scheme_name: string
          second_applicant_id: string | null
          secondary_applicant_cif_id: string | null
          sip_amount: number | null
          sip_frequency: string | null
          sip_installments: number | null
          sip_next_due: string | null
          start_date: string | null
          status: string | null
          supports_sip: boolean | null
          tds_amount: number | null
          tenure_months: number | null
          updated_at: string | null
        }
        Insert: {
          actual_profit: number
          amount: number
          client_id: string
          commission_percentage?: number | null
          compounding_frequency?: string | null
          created_at?: string | null
          id?: string
          interest_rate: number
          interest_type: string
          investment_date: string
          investment_type?: string | null
          is_active?: boolean | null
          lock_in_period_months: number
          maturity_amount: number
          maturity_date: string
          max_amount?: number | null
          min_amount?: number | null
          nominee_id?: string | null
          payout_status?: string | null
          primary_applicant_cif_id?: string | null
          reinvestment_source_id?: string | null
          remark?: string | null
          scheme_code: string
          scheme_id: string
          scheme_name: string
          second_applicant_id?: string | null
          secondary_applicant_cif_id?: string | null
          sip_amount?: number | null
          sip_frequency?: string | null
          sip_installments?: number | null
          sip_next_due?: string | null
          start_date?: string | null
          status?: string | null
          supports_sip?: boolean | null
          tds_amount?: number | null
          tenure_months?: number | null
          updated_at?: string | null
        }
        Update: {
          actual_profit?: number
          amount?: number
          client_id?: string
          commission_percentage?: number | null
          compounding_frequency?: string | null
          created_at?: string | null
          id?: string
          interest_rate?: number
          interest_type?: string
          investment_date?: string
          investment_type?: string | null
          is_active?: boolean | null
          lock_in_period_months?: number
          maturity_amount?: number
          maturity_date?: string
          max_amount?: number | null
          min_amount?: number | null
          nominee_id?: string | null
          payout_status?: string | null
          primary_applicant_cif_id?: string | null
          reinvestment_source_id?: string | null
          remark?: string | null
          scheme_code?: string
          scheme_id?: string
          scheme_name?: string
          second_applicant_id?: string | null
          secondary_applicant_cif_id?: string | null
          sip_amount?: number | null
          sip_frequency?: string | null
          sip_installments?: number | null
          sip_next_due?: string | null
          start_date?: string | null
          status?: string | null
          supports_sip?: boolean | null
          tds_amount?: number | null
          tenure_months?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "investments_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "investments_nominee_id_fkey"
            columns: ["nominee_id"]
            isOneToOne: false
            referencedRelation: "nominees"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "investments_reinvestment_source_id_fkey"
            columns: ["reinvestment_source_id"]
            isOneToOne: false
            referencedRelation: "investments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "investments_scheme_id_fkey"
            columns: ["scheme_id"]
            isOneToOne: false
            referencedRelation: "schemes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "investments_second_applicant_id_fkey"
            columns: ["second_applicant_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
        ]
      }
      nominees: {
        Row: {
          birthdate: string | null
          client_id: string | null
          created_at: string | null
          document_url: string | null
          id: string
          name: string
          relation: string
          updated_at: string | null
        }
        Insert: {
          birthdate?: string | null
          client_id?: string | null
          created_at?: string | null
          document_url?: string | null
          id?: string
          name: string
          relation: string
          updated_at?: string | null
        }
        Update: {
          birthdate?: string | null
          client_id?: string | null
          created_at?: string | null
          document_url?: string | null
          id?: string
          name?: string
          relation?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "nominees_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
        ]
      }
      notification_settings: {
        Row: {
          admin_address: string | null
          admin_email: string | null
          admin_logo_url: string | null
          admin_name: string | null
          admin_phone: string | null
          admin_tagline: string | null
          alert_days_before: number | null
          created_at: string | null
          default_commission: number | null
          default_email_from: string | null
          email_enabled: boolean | null
          id: string
          notification_time: string | null
          post_office_logo_url: string | null
          sendgrid_api_key: string | null
          sendgrid_sender_email: string | null
          sms_enabled: boolean | null
          smtp_host: string | null
          smtp_password: string | null
          smtp_port: number | null
          smtp_username: string | null
          tds_percentage: number | null
          twilio_account_sid: string | null
          twilio_auth_token: string | null
          twilio_phone_number: string | null
          twilio_whatsapp_from: string | null
          updated_at: string | null
          whatsapp_enabled: boolean | null
        }
        Insert: {
          admin_address?: string | null
          admin_email?: string | null
          admin_logo_url?: string | null
          admin_name?: string | null
          admin_phone?: string | null
          admin_tagline?: string | null
          alert_days_before?: number | null
          created_at?: string | null
          default_commission?: number | null
          default_email_from?: string | null
          email_enabled?: boolean | null
          id?: string
          notification_time?: string | null
          post_office_logo_url?: string | null
          sendgrid_api_key?: string | null
          sendgrid_sender_email?: string | null
          sms_enabled?: boolean | null
          smtp_host?: string | null
          smtp_password?: string | null
          smtp_port?: number | null
          smtp_username?: string | null
          tds_percentage?: number | null
          twilio_account_sid?: string | null
          twilio_auth_token?: string | null
          twilio_phone_number?: string | null
          twilio_whatsapp_from?: string | null
          updated_at?: string | null
          whatsapp_enabled?: boolean | null
        }
        Update: {
          admin_address?: string | null
          admin_email?: string | null
          admin_logo_url?: string | null
          admin_name?: string | null
          admin_phone?: string | null
          admin_tagline?: string | null
          alert_days_before?: number | null
          created_at?: string | null
          default_commission?: number | null
          default_email_from?: string | null
          email_enabled?: boolean | null
          id?: string
          notification_time?: string | null
          post_office_logo_url?: string | null
          sendgrid_api_key?: string | null
          sendgrid_sender_email?: string | null
          sms_enabled?: boolean | null
          smtp_host?: string | null
          smtp_password?: string | null
          smtp_port?: number | null
          smtp_username?: string | null
          tds_percentage?: number | null
          twilio_account_sid?: string | null
          twilio_auth_token?: string | null
          twilio_phone_number?: string | null
          twilio_whatsapp_from?: string | null
          updated_at?: string | null
          whatsapp_enabled?: boolean | null
        }
        Relationships: []
      }
      role_permissions: {
        Row: {
          can_add: boolean | null
          can_delete: boolean | null
          can_edit: boolean | null
          can_view: boolean | null
          created_at: string | null
          id: string
          module: string
          role_id: string | null
          updated_at: string | null
        }
        Insert: {
          can_add?: boolean | null
          can_delete?: boolean | null
          can_edit?: boolean | null
          can_view?: boolean | null
          created_at?: string | null
          id?: string
          module: string
          role_id?: string | null
          updated_at?: string | null
        }
        Update: {
          can_add?: boolean | null
          can_delete?: boolean | null
          can_edit?: boolean | null
          can_view?: boolean | null
          created_at?: string | null
          id?: string
          module?: string
          role_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "role_permissions_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["id"]
          },
        ]
      }
      roles: {
        Row: {
          created_at: string | null
          description: string | null
          id: string
          name: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          id?: string
          name: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          id?: string
          name?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      sb_accounts: {
        Row: {
          account_type: string
          already_opened: boolean
          closed_at: string | null
          created_at: string
          id: string
          is_deleted: boolean
          opened_at: string | null
          opened_by_agency: boolean
          opening_balance: number | null
          remarks: string | null
          sb_account_number: string
          status: string
          updated_at: string
        }
        Insert: {
          account_type: string
          already_opened?: boolean
          closed_at?: string | null
          created_at?: string
          id?: string
          is_deleted?: boolean
          opened_at?: string | null
          opened_by_agency?: boolean
          opening_balance?: number | null
          remarks?: string | null
          sb_account_number: string
          status?: string
          updated_at?: string
        }
        Update: {
          account_type?: string
          already_opened?: boolean
          closed_at?: string | null
          created_at?: string
          id?: string
          is_deleted?: boolean
          opened_at?: string | null
          opened_by_agency?: boolean
          opening_balance?: number | null
          remarks?: string | null
          sb_account_number?: string
          status?: string
          updated_at?: string
        }
        Relationships: []
      }
      schemes: {
        Row: {
          commission_percentage: number | null
          compounding_frequency: string | null
          created_at: string | null
          id: string
          interest_rate: number
          interest_type: string
          is_active: boolean | null
          lock_in_period_months: number | null
          max_amount: number | null
          max_sip_amount: number | null
          min_amount: number
          min_sip_amount: number | null
          name: string
          payout_type: string
          scheme_code: string
          supports_sip: boolean | null
          tenure_months: number
          updated_at: string | null
        }
        Insert: {
          commission_percentage?: number | null
          compounding_frequency?: string | null
          created_at?: string | null
          id?: string
          interest_rate: number
          interest_type: string
          is_active?: boolean | null
          lock_in_period_months?: number | null
          max_amount?: number | null
          max_sip_amount?: number | null
          min_amount: number
          min_sip_amount?: number | null
          name: string
          payout_type: string
          scheme_code: string
          supports_sip?: boolean | null
          tenure_months: number
          updated_at?: string | null
        }
        Update: {
          commission_percentage?: number | null
          compounding_frequency?: string | null
          created_at?: string | null
          id?: string
          interest_rate?: number
          interest_type?: string
          is_active?: boolean | null
          lock_in_period_months?: number | null
          max_amount?: number | null
          max_sip_amount?: number | null
          min_amount?: number
          min_sip_amount?: number | null
          name?: string
          payout_type?: string
          scheme_code?: string
          supports_sip?: boolean | null
          tenure_months?: number
          updated_at?: string | null
        }
        Relationships: []
      }
      transactions: {
        Row: {
          amount: number
          amount_type: string
          created_at: string | null
          id: string
          investment_id: string | null
          payment_mode: string | null
          reference_number: string | null
          remark: string | null
          sb_account_id: string | null
          transaction_date: string
          updated_at: string | null
        }
        Insert: {
          amount: number
          amount_type: string
          created_at?: string | null
          id?: string
          investment_id?: string | null
          payment_mode?: string | null
          reference_number?: string | null
          remark?: string | null
          sb_account_id?: string | null
          transaction_date: string
          updated_at?: string | null
        }
        Update: {
          amount?: number
          amount_type?: string
          created_at?: string | null
          id?: string
          investment_id?: string | null
          payment_mode?: string | null
          reference_number?: string | null
          remark?: string | null
          sb_account_id?: string | null
          transaction_date?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "transactions_investment_id_fkey"
            columns: ["investment_id"]
            isOneToOne: false
            referencedRelation: "investments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "transactions_sb_account_id_fkey"
            columns: ["sb_account_id"]
            isOneToOne: false
            referencedRelation: "sb_accounts"
            referencedColumns: ["id"]
          },
        ]
      }
      users: {
        Row: {
          created_at: string | null
          id: string
          is_deleted: boolean | null
          mobile: string | null
          role_id: string | null
          updated_at: string | null
          username: string | null
        }
        Insert: {
          created_at?: string | null
          id: string
          is_deleted?: boolean | null
          mobile?: string | null
          role_id?: string | null
          updated_at?: string | null
          username?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          is_deleted?: boolean | null
          mobile?: string | null
          role_id?: string | null
          updated_at?: string | null
          username?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "users_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      bytea_to_text: {
        Args: { data: string }
        Returns: string
      }
      get_email_by_phone: {
        Args: { phone_number: string }
        Returns: string
      }
      get_user_role: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      http: {
        Args: { request: Database["public"]["CompositeTypes"]["http_request"] }
        Returns: Database["public"]["CompositeTypes"]["http_response"]
      }
      http_delete: {
        Args:
          | { uri: string }
          | { uri: string; content: string; content_type: string }
        Returns: Database["public"]["CompositeTypes"]["http_response"]
      }
      http_get: {
        Args: { uri: string } | { uri: string; data: Json }
        Returns: Database["public"]["CompositeTypes"]["http_response"]
      }
      http_head: {
        Args: { uri: string }
        Returns: Database["public"]["CompositeTypes"]["http_response"]
      }
      http_header: {
        Args: { field: string; value: string }
        Returns: Database["public"]["CompositeTypes"]["http_header"]
      }
      http_list_curlopt: {
        Args: Record<PropertyKey, never>
        Returns: {
          curlopt: string
          value: string
        }[]
      }
      http_patch: {
        Args: { uri: string; content: string; content_type: string }
        Returns: Database["public"]["CompositeTypes"]["http_response"]
      }
      http_post: {
        Args:
          | { uri: string; content: string; content_type: string }
          | { uri: string; data: Json }
        Returns: Database["public"]["CompositeTypes"]["http_response"]
      }
      http_put: {
        Args: { uri: string; content: string; content_type: string }
        Returns: Database["public"]["CompositeTypes"]["http_response"]
      }
      http_reset_curlopt: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      http_set_curlopt: {
        Args: { curlopt: string; value: string }
        Returns: boolean
      }
      is_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      send_alerts: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      send_maturity_alerts_email: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      text_to_bytea: {
        Args: { data: string }
        Returns: string
      }
      trigger_maturity_alerts: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      update_matured_investments: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      urlencode: {
        Args: { data: Json } | { string: string } | { string: string }
        Returns: string
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      http_header: {
        field: string | null
        value: string | null
      }
      http_request: {
        method: unknown | null
        uri: string | null
        headers: Database["public"]["CompositeTypes"]["http_header"][] | null
        content_type: string | null
        content: string | null
      }
      http_response: {
        status: number | null
        content_type: string | null
        headers: Database["public"]["CompositeTypes"]["http_header"][] | null
        content: string | null
      }
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
