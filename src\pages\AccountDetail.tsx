import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Edit, Trash2 } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from '@/hooks/use-toast';

interface SBAccount {
    id: string;
    sb_account_number: string;
    account_type: string;
    status: string;
    opening_balance: number | null;
    opened_at: string | null;
    opened_by_agency: boolean;
    already_opened: boolean;
    remarks: string | null;
    created_at: string;
    client_sb_accounts: Array<{
        role: string;
        clients: {
            id: string;
            first_name: string;
            last_name: string;
            email: string;
            mobile_number: string;
        };
    }>;
}

const AccountDetail: React.FC = () => {
    const { id } = useParams<{ id: string }>();
    const navigate = useNavigate();
    const [loading, setLoading] = useState(true);
    const [account, setAccount] = useState<SBAccount | null>(null);
    interface Transaction {
        id: string;
        transaction_date: string | null;
        amount_type: string;
        amount: number;
        reference_number: string;
        remark: string;
        // Add other fields as needed based on your transactions table
    }
    
    const [transactions, setTransactions] = useState<Transaction[]>([]);

    useEffect(() => {
        if (id) {
            fetchAccount();
            fetchTransactions();
        }
    }, [id]);

    const fetchAccount = async () => {
        try {
            const { data, error } = await supabase
                .from('sb_accounts')
                .select(`
          *,
          client_sb_accounts (
            role,
            clients (
              id,
              first_name,
              last_name,
              email,
              mobile_number
            )
          )
        `)
                .eq('id', id)
                .eq('is_deleted', false)
                .single();

            if (error) throw error;
            setAccount(data);
        } catch (error) {
            console.error('Error fetching account:', error);
            toast({
                title: "Error",
                description: "Failed to fetch account details",
                variant: "destructive",
            });
            navigate('/accounts');
        } finally {
            setLoading(false);
        }
    };

    const fetchTransactions = async () => {
        try {
            const { data, error } = await supabase
                .from('transactions')
                .select('*')
                .eq('sb_account_id', id)
                .order('transaction_date', { ascending: false });
            if (error) throw error;
            setTransactions(data || []);
        } catch (error) {
            console.error('Error fetching transactions:', error);
            toast({
                title: "Error",
                description: "Failed to fetch transactions",
                variant: "destructive",
            });
        }
    };

    const handleDelete = async () => {
        if (!confirm('Are you sure you want to delete this account?')) return;

        try {
            const { error } = await supabase
                .from('sb_accounts')
                .update({ is_deleted: true })
                .eq('id', id);

            if (error) throw error;

            toast({
                title: "Success",
                description: "Account deleted successfully",
            });

            navigate('/accounts');
        } catch (error) {
            console.error('Error deleting account:', error);
            toast({
                title: "Error",
                description: "Failed to delete account",
                variant: "destructive",
            });
        }
    };

    const getStatusBadge = (status: string) => {
        const colors = {
            active: 'bg-green-100 text-green-800',
            closed: 'bg-red-100 text-red-800',
            suspended: 'bg-yellow-100 text-yellow-800',
        };
        return colors[status] || 'bg-gray-100 text-gray-800';
    };

    const getTypeBadge = (type: string) => {
        const colors = {
            single: 'bg-blue-100 text-blue-800',
            joint: 'bg-purple-100 text-purple-800',
        };
        return colors[type] || 'bg-gray-100 text-gray-800';
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
                    <p>Loading account details...</p>
                </div>
            </div>
        );
    }

    if (!account) {
        return (
            <div className="text-center py-8">
                <p className="text-gray-500">Account not found</p>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                    <Button variant="ghost" onClick={() => navigate('/accounts')}>
                        <ArrowLeft className="h-4 w-4" />
                    </Button>
                    <h1 className="text-3xl font-bold">Account Details</h1>
                </div>
                <div className="flex gap-2">
                    <Button variant="outline" onClick={() => navigate(`/accounts/${id}/edit`)}>
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                    </Button>
                    <Button variant="destructive" onClick={handleDelete}>
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                    </Button>
                </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Account Information */}
                <Card>
                    <CardHeader>
                        <CardTitle>Account Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div>
                            <label className="text-sm font-medium text-gray-500">Account Number</label>
                            <p className="text-lg font-semibold">{account.sb_account_number}</p>
                        </div>
                        <div>
                            <label className="text-sm font-medium text-gray-500">Account Type</label>
                            <div className="mt-1">
                                <Badge className={getTypeBadge(account.account_type)}>
                                    {account.account_type}
                                </Badge>
                            </div>
                        </div>
                        <div>
                            <label className="text-sm font-medium text-gray-500">Status</label>
                            <div className="mt-1">
                                <Badge className={getStatusBadge(account.status)}>
                                    {account.status}
                                </Badge>
                            </div>
                        </div>
                        <div>
                            <label className="text-sm font-medium text-gray-500">Opening Balance</label>
                            <p className="text-lg">
                                {account.opening_balance
                                    ? `₹${account.opening_balance.toLocaleString()}`
                                    : 'N/A'
                                }
                            </p>
                        </div>
                        <div>
                            <label className="text-sm font-medium text-gray-500">Created By</label>
                            <div className="mt-1">
                                <Badge className={account.opened_by_agency ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}>
                                    {account.opened_by_agency ? 'Agency' : 'External'}
                                </Badge>
                            </div>
                        </div>
                        <div>
                            <label className="text-sm font-medium text-gray-500">Opened At</label>
                            <p className="text-lg">
                                {account.opened_at
                                    ? new Date(account.opened_at).toLocaleDateString()
                                    : 'N/A'
                                }
                            </p>
                        </div>
                        <div>
                            <label className="text-sm font-medium text-gray-500">Created At</label>
                            <p className="text-lg">
                                {new Date(account.created_at).toLocaleDateString()}
                            </p>
                        </div>
                        {account.remarks && (
                            <div>
                                <label className="text-sm font-medium text-gray-500">Remarks</label>
                                <p className="text-lg">{account.remarks}</p>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Associated Clients */}
                <Card>
                    <CardHeader>
                        <CardTitle>Associated Clients</CardTitle>
                    </CardHeader>
                    <CardContent>
                        {account.client_sb_accounts.length === 0 ? (
                            <p className="text-gray-500">No clients associated with this account</p>
                        ) : (
                            <div className="space-y-4">
                                {account.client_sb_accounts.map((csa, index) => (
                                    <div key={index} className="border rounded-lg p-4">
                                        <div className="flex justify-between items-start mb-2">
                                            <h4 className="font-medium">
                                                {csa.clients.first_name} {csa.clients.last_name}
                                            </h4>
                                            <Badge variant="outline">{csa.role}</Badge>
                                        </div>
                                        <div className="space-y-1 text-sm text-gray-600">
                                            <p><strong>Email:</strong> {csa.clients.email || 'N/A'}</p>
                                            <p><strong>Mobile:</strong> {csa.clients.mobile_number}</p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
            {/* Transactions Section */}
            <div className="mt-8">
                <Card>
                    <CardHeader>
                        <CardTitle>Transactions for this Account</CardTitle>
                    </CardHeader>
                    <CardContent className="overflow-x-auto">
                        {transactions.length === 0 ? (
                            <p className="text-gray-500">No transactions found for this account.</p>
                        ) : (
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Date</th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Reference</th>
                                        <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Remark</th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {transactions.map((txn) => (
                                        <tr key={txn.id}>
                                            <td className="px-4 py-2 whitespace-nowrap">{txn.transaction_date ? new Date(txn.transaction_date).toLocaleDateString() : ''}</td>
                                            <td className="px-4 py-2 whitespace-nowrap capitalize">{txn.amount_type?.replace('_', ' ')}</td>
                                            <td className="px-4 py-2 whitespace-nowrap">₹{txn.amount?.toLocaleString()}</td>
                                            <td className="px-4 py-2 whitespace-nowrap">{txn.reference_number}</td>
                                            <td className="px-4 py-2 whitespace-nowrap">{txn.remark}</td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        )}
                    </CardContent>
                </Card>
            </div>
        </div>
    );
};

export default AccountDetail;